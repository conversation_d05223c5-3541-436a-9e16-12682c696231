/**
 * 第三方系统集成配置
 */

// 迎春花质控系统配置
export const yingchunhuaConfig = {
  // 开发环境配置
  development: {
    sdkUrl: 'http://183.242.68.188:32103/client_app_iframe/index.js',
    appKey: 'ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09', // 已提供
    appSecretKey: '', // 待项目经理提供
    linkType: '2', // 1-app, 2-web
    qualityTarget: '2' // 1-临床, 2-病理
  },
  
  // 测试环境配置
  testing: {
    sdkUrl: 'http://183.242.68.188:8094/client_app_iframe/index.js',
    appKey: 'ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09',
    appSecretKey: '', // 待项目经理提供
    linkType: '2',
    qualityTarget: '2'
  },

  // 生产环境配置
  production: {
    sdkUrl: 'http://183.242.68.188:8094/client_app_iframe/index.js',
    appKey: 'ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09',
    appSecretKey: '', // 待项目经理提供
    linkType: '2',
    qualityTarget: '2'
  }
}

// DRG系统配置
export const drgConfig = {
  development: {
    apiUrl: '',
    appKey: '',
    appSecret: ''
  },
  testing: {
    apiUrl: '',
    appKey: '',
    appSecret: ''
  },
  production: {
    apiUrl: '',
    appKey: '',
    appSecret: ''
  }
}

// 获取当前环境配置
export const getCurrentConfig = (system = 'yingchunhua') => {
  const env = process.env.NODE_ENV || 'development'
  
  const configs = {
    yingchunhua: yingchunhuaConfig,
    drg: drgConfig
  }
  
  return configs[system]?.[env] || configs[system]?.development
}

// 集成系统列表
export const integrationSystems = [
  {
    id: 'yingchunhua',
    name: '迎春花质控系统',
    description: '医疗质量控制系统',
    modules: [
      { id: 'process', name: '过程管理', patientTypes: ['在院', '门诊'] },
      { id: 'quality', name: '质控结果', patientTypes: ['在院', '门诊'] },
      { id: 'log', name: '质控日志', patientTypes: ['在院', '出院'] },
      { id: 'cost', name: '费用管理', patientTypes: ['在院', '出院'], note: '对接DRG系统' },
      { id: 'summary', name: '质控小结', patientTypes: ['出院'] }
    ],
    supportedScenarios: [
      'HIS-在院患者选中',
      'HIS-门诊患者选中', 
      'HIS-出院患者选中',
      'EMR-在院患者选中',
      'EMR-出院患者选中'
    ]
  },
  {
    id: 'drg',
    name: 'DRG系统',
    description: '诊断相关分组系统',
    modules: [
      { id: 'grouping', name: 'DRG分组', patientTypes: ['出院'] },
      { id: 'cost_analysis', name: '费用分析', patientTypes: ['出院'] }
    ]
  }
]

// 患者数据字段映射配置
export const patientDataMapping = {
  // 必填字段
  required: [
    'patient_id',
    'visit_sn', 
    'visit_type',
    'hospital_code',
    'hospital_name',
    'visit_doctor_no',
    'visit_doctor_name',
    'name',
    'gender',
    'date_of_birth',
    'occupation_code',
    'occupation_name'
  ],
  
  // 住院患者必填字段
  inpatientRequired: [
    'medical_record_no',
    'inpatient_no',
    'admission_datetime'
  ],
  
  // 门诊患者必填字段
  outpatientRequired: [
    'outpatient_no',
    'visit_datetime',
    'regis_sn',
    'regis_datetime',
    'regis_dept_code',
    'regis_dept_name'
  ],
  
  // 字段默认值
  defaults: {
    patient_gender: 'NULL',
    nationality: '中国',
    ethnicity: '汉族',
    newbron_mark: '否',
    visit_status: '否',
    patient_identity: '其他',
    blood_type_s: 'NULL',
    bolld_type_e: 'NULL',
    height: 'NULL',
    weight: 'NULL',
    certificate_type: '身份证',
    health_card_type: 'NULL',
    health_card_no: 'NULL',
    tsblbs: 'NULL',
    is_hospital_infected: 'NULL',
    extend_data1: 'NULL',
    extend_data2: 'NULL',
    record_status: '1',
    first_visit_mark: '是',
    regis_charge_price: '0.000',
    regis_paid_price: '0.000'
  }
}

// 表代码映射
export const tableCodeMapping = {
  '住院': 'b02_1',
  '门诊': 'b12_1',
  '体检': 'b12_1'
}

// 质控目标映射
export const qualityTargetMapping = {
  '1': '临床',
  '2': '病理'
}

// 链接类型映射
export const linkTypeMapping = {
  '1': 'app',
  '2': 'web'
}

// 浏览器兼容性检查
export const browserCompatibility = {
  minVersions: {
    ie: 11,
    chrome: 49,
    firefox: 45,
    safari: 10,
    edge: 12
  },
  
  checkCompatibility() {
    const userAgent = navigator.userAgent
    const isCompatible = {
      ie: /MSIE|Trident/.test(userAgent),
      chrome: /Chrome/.test(userAgent),
      firefox: /Firefox/.test(userAgent),
      safari: /Safari/.test(userAgent) && !/Chrome/.test(userAgent),
      edge: /Edge/.test(userAgent)
    }
    
    return {
      isSupported: Object.values(isCompatible).some(Boolean),
      detectedBrowser: Object.keys(isCompatible).find(key => isCompatible[key]) || 'unknown',
      userAgent
    }
  }
}

export default {
  yingchunhuaConfig,
  drgConfig,
  getCurrentConfig,
  integrationSystems,
  patientDataMapping,
  tableCodeMapping,
  qualityTargetMapping,
  linkTypeMapping,
  browserCompatibility
}

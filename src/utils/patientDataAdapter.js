/**
 * 患者数据适配器
 * 用于将系统内部的患者数据格式转换为第三方系统要求的格式
 */

import { patientDataMapping, tableCodeMapping } from '@/config/thirdPartyConfig'

/**
 * 患者数据适配器类
 */
export class PatientDataAdapter {
  /**
   * 将内部患者数据转换为迎春花系统格式
   * @param {Object} internalData - 内部患者数据
   * @returns {Object} 转换后的数据
   */
  static toYingchunhuaFormat(internalData) {
    const { patient, visit, hospital } = internalData
    
    // 确定表代码
    const tableCode = tableCodeMapping[visit.visitType] || 'b02_1'
    
    // 基础患者数据
    const baseData = {
      patient_id: patient.id || patient.patientId,
      visit_sn: visit.visitSn || this.generateVisitSn(patient.id, visit),
      visit_type: visit.visitType || '住院',
      hospital_code: hospital?.code || '',
      hospital_name: hospital?.name || '',
      visit_card_no: visit.visitCardNo || '',
      visit_doctor_no: visit.doctorNo || '',
      visit_doctor_name: visit.doctorName || '',
      name: patient.name,
      gender: patient.gender,
      patient_gender: this.mapGender(patient.gender),
      date_of_birth: this.formatDate(patient.dateOfBirth),
      occupation_code: patient.occupationCode || '',
      occupation_name: patient.occupationName || '',
      nationality: patient.nationality || patientDataMapping.defaults.nationality,
      ethnicity: patient.ethnicity || patientDataMapping.defaults.ethnicity,
      education: patient.education || '',
      education_code: patient.educationCode || '',
      marital_status: patient.maritalStatus || '',
      marital_status_code: patient.maritalStatusCode || '',
      newbron_mark: patient.isNewborn ? '是' : '否',
      visit_status: visit.isInHospital ? '是' : '否',
      patient_identity: patient.identity || patientDataMapping.defaults.patient_identity,
      blood_type_s: patient.bloodTypeS || patientDataMapping.defaults.blood_type_s,
      bolld_type_e: patient.bloodTypeE || patientDataMapping.defaults.bolld_type_e,
      height: patient.height || patientDataMapping.defaults.height,
      weight: patient.weight || patientDataMapping.defaults.weight,
      certificate_type: patient.certificateType || patientDataMapping.defaults.certificate_type,
      certificate_no: patient.certificateNo || '',
      idcard_no: patient.idCardNo || patient.certificateNo || '',
      health_card_type: patient.healthCardType || patientDataMapping.defaults.health_card_type,
      health_card_no: patient.healthCardNo || patientDataMapping.defaults.health_card_no,
      insurance_type: patient.insuranceType || '',
      insurance_no: patient.insuranceNo || '',
      domicile_province: patient.domicileProvince || '',
      domicile_city: patient.domicileCity || '',
      domicile_county: patient.domicileCounty || '',
      domicile_address: patient.domicileAddress || '',
      home_address: patient.homeAddress || '',
      phone_no: patient.phoneNo || '',
      phone_no2: patient.phoneNo2 || '',
      email: patient.email || '',
      weixin: patient.weixin || '',
      contact_person1: patient.contactPerson1 || '',
      contact_phone_no1: patient.contactPhoneNo1 || '',
      contact_person2: patient.contactPerson2 || '',
      contact_phone_no2: patient.contactPhoneNo2 || '',
      abo_blood_type: patient.aboBloodType || '',
      rh_blood_type: patient.rhBloodType || '',
      tsblbs: patient.specialMark || patientDataMapping.defaults.tsblbs,
      is_hospital_infected: patient.isHospitalInfected || patientDataMapping.defaults.is_hospital_infected,
      extend_data1: patient.extendData1 || patientDataMapping.defaults.extend_data1,
      extend_data2: patient.extendData2 || patientDataMapping.defaults.extend_data2,
      record_status: patient.recordStatus || patientDataMapping.defaults.record_status,
      record_datetime: this.formatDateTime(patient.recordDatetime || visit.createTime),
      record_update_datetime: this.formatDateTime(patient.recordUpdateDatetime || visit.updateTime)
    }

    // 根据就诊类型添加特定字段
    if (visit.visitType === '住院') {
      Object.assign(baseData, this.getInpatientFields(visit))
    } else if (visit.visitType === '门诊') {
      Object.assign(baseData, this.getOutpatientFields(visit))
    }

    return {
      patientId: baseData.patient_id,
      visitSn: baseData.visit_sn,
      dataPacket: [{
        tableCode: tableCode,
        data: [baseData]
      }]
    }
  }

  /**
   * 获取住院患者特有字段
   * @param {Object} visit - 就诊信息
   * @returns {Object} 住院字段
   */
  static getInpatientFields(visit) {
    return {
      medical_record_no: visit.medicalRecordNo || '',
      inpatient_no: visit.inpatientNo || '',
      hospitalization_times: visit.hospitalizationTimes || '1',
      admission_datetime: this.formatDateTime(visit.admissionDatetime),
      discharge_datetime: this.formatDateTime(visit.dischargeDatetime),
      admission_dept_code: visit.admissionDeptCode || '',
      admission_dept_name: visit.admissionDeptName || '',
      admission_ward_code: visit.admissionWardCode || '',
      admission_ward_name: visit.admissionWardName || '',
      admission_bed_code: visit.admissionBedCode || '',
      admission_bed_name: visit.admissionBedName || '',
      current_dept_code: visit.currentDeptCode || '',
      current_dept_name: visit.currentDeptName || '',
      current_ward_code: visit.currentWardCode || '',
      current_ward_name: visit.currentWardName || '',
      current_bed_code: visit.currentBedCode || '',
      current_bed_name: visit.currentBedName || '',
      admission_medical_team_code: visit.medicalTeamCode || '',
      admission_medical_team_name: visit.medicalTeamName || '',
      chief_physician_id: visit.chiefPhysicianId || '',
      chief_physician: visit.chiefPhysician || '',
      attending_physician_id: visit.attendingPhysicianId || '',
      attending_physician: visit.attendingPhysician || '',
      responsible_nurse_id: visit.responsibleNurseId || '',
      responsible_nurse: visit.responsibleNurse || '',
      admission_type_code: visit.admissionTypeCode || '',
      admission_type_name: visit.admissionTypeName || ''
    }
  }

  /**
   * 获取门诊患者特有字段
   * @param {Object} visit - 就诊信息
   * @returns {Object} 门诊字段
   */
  static getOutpatientFields(visit) {
    return {
      outpatient_no: visit.outpatientNo || '',
      visit_times: visit.visitTimes || '1',
      visit_datetime: this.formatDateTime(visit.visitDatetime),
      regis_sn: visit.registrationSn || '',
      regis_datetime: this.formatDateTime(visit.registrationDatetime),
      first_visit_mark: visit.isFirstVisit ? '是' : '否',
      regis_method_code: visit.registrationMethodCode || '',
      regis_method: visit.registrationMethod || '',
      regis_type_code: visit.registrationTypeCode || '',
      regis_type: visit.registrationType || '',
      regis_charge_price: this.formatPrice(visit.registrationChargePrice),
      regis_paid_price: this.formatPrice(visit.registrationPaidPrice),
      regis_dept_code: visit.registrationDeptCode || '',
      regis_dept_name: visit.registrationDeptName || '',
      technical_title: visit.doctorTechnicalTitle || '',
      job_title: visit.doctorJobTitle || ''
    }
  }

  /**
   * 生成就诊流水号
   * @param {string} patientId - 患者ID
   * @param {Object} visit - 就诊信息
   * @returns {string} 就诊流水号
   */
  static generateVisitSn(patientId, visit) {
    const visitType = visit.visitType === '住院' ? '1住院' : '2门诊'
    const visitNo = visit.inpatientNo || visit.outpatientNo || '001'
    return `${patientId}|${visitNo}|${visitType}`
  }

  /**
   * 映射性别
   * @param {string} gender - 性别
   * @returns {string} 映射后的性别
   */
  static mapGender(gender) {
    const genderMap = {
      '男': '1',
      '女': '2',
      '未知': '0',
      '其他': '9'
    }
    return genderMap[gender] || 'NULL'
  }

  /**
   * 格式化日期
   * @param {string|Date} date - 日期
   * @returns {string} 格式化后的日期 (yyyy-MM-dd)
   */
  static formatDate(date) {
    if (!date) return ''
    const d = new Date(date)
    if (isNaN(d.getTime())) return ''
    
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    
    return `${year}-${month}-${day}`
  }

  /**
   * 格式化日期时间
   * @param {string|Date} datetime - 日期时间
   * @returns {string} 格式化后的日期时间 (yyyy-MM-dd HH:mm:ss)
   */
  static formatDateTime(datetime) {
    if (!datetime) return ''
    const d = new Date(datetime)
    if (isNaN(d.getTime())) return ''
    
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  /**
   * 格式化价格
   * @param {number|string} price - 价格
   * @returns {string} 格式化后的价格
   */
  static formatPrice(price) {
    if (!price && price !== 0) return '0.000'
    const num = parseFloat(price)
    if (isNaN(num)) return '0.000'
    return num.toFixed(3)
  }

  /**
   * 验证必填字段
   * @param {Object} data - 数据对象
   * @param {string} visitType - 就诊类型
   * @returns {Object} 验证结果
   */
  static validateRequiredFields(data, visitType) {
    const errors = []
    const requiredFields = [...patientDataMapping.required]
    
    // 根据就诊类型添加特定必填字段
    if (visitType === '住院') {
      requiredFields.push(...patientDataMapping.inpatientRequired)
    } else if (visitType === '门诊') {
      requiredFields.push(...patientDataMapping.outpatientRequired)
    }
    
    // 检查必填字段
    requiredFields.forEach(field => {
      if (!data[field] || data[field] === '') {
        errors.push(`缺少必填字段: ${field}`)
      }
    })
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 从API响应转换患者数据
   * @param {Object} apiResponse - API响应数据
   * @returns {Object} 转换后的患者数据
   */
  static fromApiResponse(apiResponse) {
    // 根据实际API响应格式进行转换
    return {
      patient: {
        id: apiResponse.patient_id,
        name: apiResponse.name,
        gender: apiResponse.gender,
        dateOfBirth: apiResponse.date_of_birth,
        phoneNo: apiResponse.phone_no,
        // ... 其他字段映射
      },
      visit: {
        visitSn: apiResponse.visit_sn,
        visitType: apiResponse.visit_type,
        admissionDatetime: apiResponse.admission_datetime,
        // ... 其他字段映射
      },
      hospital: {
        code: apiResponse.hospital_code,
        name: apiResponse.hospital_name
      }
    }
  }
}

export default PatientDataAdapter

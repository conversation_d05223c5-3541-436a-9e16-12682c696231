<template>
  <div class="medical-order-info-container">
    <!-- 左侧患者信息详情面板 -->
    <PatientInfoPanel :patient="patient"/>

    <!-- 右侧医嘱信息内容区域 -->
    <div class="medical-order-content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载医嘱信息...">
        <div style="height: 200px;"></div>
      </div>

      <!-- 内容区域 -->
      <div v-else class="content-wrapper">
        <!-- 页面标题 -->
        <div class="page-title">
          <h2 class="title-text">医嘱信息</h2>
        </div>

        <!-- 医嘱信息表格 -->
        <div class="medical-order-table-container">
          <table class="medical-order-table">
            <!-- 表头 -->
            <thead>
            <tr class="table-header">
              <th class="header-cell">
                <span class="required-field">*</span>医嘱类型
              </th>
              <th class="header-cell">
                <span class="required-field">*</span>医嘱类别
              </th>
              <th class="header-cell">
                <span class="required-field">*</span>医嘱名称
              </th>
              <th class="header-cell">
                <span class="required-field">*</span>药品规格
              </th>
              <th class="header-cell">
                <span class="required-field">*</span>频次
              </th>
              <th class="header-cell">
                <span class="required-field">*</span>用法
              </th>
              <th class="header-cell">
                <span class="required-field">*</span>用量
              </th>
              <th class="header-cell">
                <span class="required-field">*</span>单位
              </th>
              <th class="header-cell">备注</th>
              <th class="header-cell">开始时间</th>
              <th class="header-cell">结束时间</th>
              <th class="header-cell">操作</th>
            </tr>
            </thead>
            <!-- 表体 -->
            <tbody>
            <!-- 数据行 -->
            <tr
                v-for="(item, index) in medicalOrderList"
                :key="item.orderSn || index"
                class="table-row"
            >
              <!-- 医嘱类型 -->
              <td class="table-cell">
                <el-select
                    v-if="editingIndex === index"
                    v-model="editForm.orderClassCode"
                    placeholder="请选择医嘱类型"
                    class="cell-input"
                    :loading="medicalOrderCategoryLoading"
                    @change="handleOrderCategoryChange"
                >
                  <el-option
                      v-for="option in medicalOrderCategoryOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
                <span v-else>{{
                    getMedicalOrderCategoryLabel(item.orderClassCode) || item.orderClassName || item.orderClassCode
                  }}</span>
              </td>

              <!-- 医嘱类别 -->
              <td class="table-cell">
                <el-select
                    v-if="editingIndex === index"
                    v-model="editForm.orderType"
                    placeholder="请选择医嘱类别"
                    class="cell-input"
                    :loading="medicalOrderTypeLoading"
                >
                  <el-option
                      v-for="option in medicalOrderTypeOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
                <span v-else>{{ getMedicalOrderTypeLabel(item.orderType) || item.orderType }}</span>
              </td>
              <!-- 医嘱名称 -->
              <td class="table-cell">
                <el-select
                    v-if="editingIndex === index"
                    v-model="editForm.orderItemName"
                    placeholder="请选择医嘱名称"
                    class="cell-input"
                    filterable
                    :loading="medicalOrderNameLoading"
                >
                  <el-option
                      v-for="option in medicalOrderNameOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
                <span v-else>{{ getMedicalOrderNameLabel(item.orderItemName) || item.orderItemName }}</span>
              </td>
              <!-- 药品规格 -->
              <td class="table-cell">
                <el-input
                    v-if="editingIndex === index && isDrugOrder(editForm.orderClassName)"
                    v-model="editForm.spec"
                    placeholder="规格信息（从备注解析）"
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else-if="editingIndex === index"
                    v-model="editForm.spec"
                    placeholder="请输入规格"
                    class="cell-input"
                />
                <span v-else>{{ item.spec || '' }}</span>
              </td>
              <!-- 频次 -->
              <td class="table-cell">
                <el-input
                    v-if="editingIndex === index && isDrugOrder(editForm.orderClassName)"
                    v-model="editForm.frequencyCode"
                    placeholder="频次信息（从备注解析）"
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else-if="editingIndex === index"
                    v-model="editForm.frequencyCode"
                    placeholder="请输入频次"
                    class="cell-input"
                />
                <span v-else>{{ item.frequencyName || item.frequencyCode || '' }}</span>
              </td>
              <!-- 用法 -->
              <td class="table-cell">
                <el-input
                    v-if="editingIndex === index && isDrugOrder(editForm.orderClassName)"
                    v-model="editForm.administrationRoute"
                    placeholder="用法信息（从备注解析）"
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else-if="editingIndex === index"
                    v-model="editForm.administrationRoute"
                    placeholder="请输入用法"
                    class="cell-input"
                />
                <span v-else>{{ item.administrationRoute || '' }}</span>
              </td>
              <!-- 用量 -->
              <td class="table-cell">
                <el-input
                    v-if="editingIndex === index && isDrugOrder(editForm.orderClassName)"
                    v-model="editForm.dose"
                    placeholder="用量信息（从备注解析）"
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else-if="editingIndex === index"
                    v-model="editForm.dose"
                    placeholder="请输入用量"
                    class="cell-input"
                />
                <span v-else>{{ item.dose || '' }}</span>
              </td>
              <!-- 单位 -->
              <td class="table-cell">
                <el-input
                    v-if="editingIndex === index && isDrugOrder(editForm.orderClassName)"
                    v-model="editForm.doseUnit"
                    placeholder="单位信息（从备注解析）"
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else-if="editingIndex === index"
                    v-model="editForm.doseUnit"
                    placeholder="请输入单位"
                    class="cell-input"
                />
                <span v-else>{{ item.doseUnit || '' }}</span>
              </td>
              <!-- 备注 -->
              <td class="table-cell">
                <el-input
                    v-if="editingIndex === index"
                    v-model="editForm.remark"
                    placeholder="请输入备注信息"
                    class="cell-input"
                    type="textarea"
                    :rows="1"
                    @blur="handleRemarkBlur"
                />
                <span v-else :title="item.remark">{{
                  item.remark ? (item.remark.length > 20 ? item.remark.substring(0, 20) + '...' : item.remark) : ''
                }}</span>
              </td>
              <!-- 开始时间 -->
              <td class="table-cell">
                <el-date-picker
                    v-if="editingIndex === index"
                    v-model="editForm.orderStartDatetime"
                    type="datetime"
                    placeholder="选择开始时间"
                    class="cell-input"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                />
                <span v-else>{{ formatDateTime(item.orderStartDatetime) }}</span>
              </td>
              <!-- 结束时间 -->
              <td class="table-cell">
                <el-date-picker
                    v-if="editingIndex === index"
                    v-model="editForm.orderEndDatetime"
                    type="datetime"
                    placeholder="选择结束时间"
                    class="cell-input"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                />
                <span v-else>{{ formatDateTime(item.orderEndDatetime) }}</span>
              </td>
              <!-- 操作 -->
              <td class="table-cell">
                <div v-if="editingIndex === index" class="action-buttons">
                  <el-button
                      type="primary"
                      size="small"
                      @click="handleSave"
                      :loading="saveLoading"
                  >
                    保存
                  </el-button>
                  <el-button
                      size="small"
                      @click="handleCancelEdit"
                  >
                    取消
                  </el-button>
                </div>
                <div v-else class="action-buttons">
                  <el-button
                      type="primary"
                      size="small"
                      @click="handleEdit(index)"
                  >
                    编辑
                  </el-button>
                  <el-button
                      type="danger"
                      size="small"
                      @click="handleDelete(item)"
                      :loading="deleteLoading"
                  >
                    删除
                  </el-button>
                </div>
              </td>
            </tr>
            <!-- 编辑添加行 -->
            <tr v-if="isAdding" class="table-row add-row">
              <td class="table-cell">
                <el-select
                    v-model="addForm.orderClassCode"
                    placeholder="请选择医嘱类型"
                    class="cell-input"
                    :loading="medicalOrderCategoryLoading"
                    @change="handleAddOrderCategoryChange"
                >
                  <el-option
                      v-for="option in medicalOrderCategoryOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
              </td>
              <td class="table-cell">
                <el-select
                    v-model="addForm.orderType"
                    placeholder="请选择医嘱类别"
                    class="cell-input"
                    :loading="medicalOrderTypeLoading"
                >
                  <el-option
                      v-for="option in medicalOrderTypeOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
              </td>
              <td class="table-cell">
                <el-select
                    v-model="addForm.orderItemName"
                    placeholder="请选择医嘱名称"
                    class="cell-input"
                    filterable
                    :loading="medicalOrderNameLoading"
                >
                  <el-option
                      v-for="option in medicalOrderNameOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
              </td>
              <td class="table-cell">
                <el-input
                    v-if="isDrugOrder(addForm.orderClassName)"
                    v-model="addForm.spec"
                    placeholder="规格信息（从备注解析）"
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else
                    v-model="addForm.spec"
                    placeholder="请输入规格"
                    class="cell-input"
                />
              </td>
              <td class="table-cell">
                <el-input
                    v-if="isDrugOrder(addForm.orderClassName)"
                    v-model="addForm.frequencyCode"
                    placeholder="频次信息（从备注解析）"
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else
                    v-model="addForm.frequencyCode"
                    placeholder="请输入频次"
                    class="cell-input"
                />
              </td>
              <td class="table-cell">
                <el-input
                    v-if="isDrugOrder(addForm.orderClassName)"
                    v-model="addForm.administrationRoute"
                    placeholder="用法信息（从备注解析）"
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else
                    v-model="addForm.administrationRoute"
                    placeholder="请输入用法"
                    class="cell-input"
                />
              </td>
              <td class="table-cell">
                <el-input
                    v-if="isDrugOrder(addForm.orderClassName)"
                    v-model="addForm.dose"
                    placeholder="用量信息（从备注解析）"
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else
                    v-model="addForm.dose"
                    placeholder="请输入用量"
                    class="cell-input"
                />
              </td>
              <td class="table-cell">
                <el-input
                    v-if="isDrugOrder(addForm.orderClassName)"
                    v-model="addForm.doseUnit"
                    placeholder="单位信息（从备注解析）"
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else
                    v-model="addForm.doseUnit"
                    placeholder="请输入单位"
                    class="cell-input"
                />
              </td>
              <td class="table-cell">
                <el-input
                    v-model="addForm.remark"
                    placeholder="请输入备注信息"
                    class="cell-input"
                    type="textarea"
                    :rows="1"
                    @blur="handleAddRemarkBlur"
                />
              </td>
              <td class="table-cell">
                <el-date-picker
                    v-model="addForm.orderStartDatetime"
                    type="datetime"
                    placeholder="选择开始时间"
                    class="cell-input"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                />
              </td>
              <td class="table-cell">
                <el-date-picker
                    v-model="addForm.orderEndDatetime"
                    type="datetime"
                    placeholder="选择结束时间"
                    class="cell-input"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                />
              </td>
              <td class="table-cell">
                <div class="action-buttons">
                  <el-button
                      type="primary"
                      size="small"
                      @click="handleSaveAdd"
                      :loading="saveLoading"
                  >
                    保存
                  </el-button>
                  <el-button
                      size="small"
                      @click="handleCancelAdd"
                  >
                    取消
                  </el-button>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
          <!-- 添加按钮 - 使用div居中 -->
          <div v-if="!isAdding" class="add-button-container">
            <el-button
                type="primary"
                @click="handleAdd"
                class="add-record-btn"
                :icon="Plus"
            >
              添加
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- TNM分期诊断提示弹窗 -->
    <div v-if="showTnmDialog" class="tnm-dialog-overlay" @click="handleTnmDialogCancel">
      <div class="tnm-dialog-container" @click.stop>
        <div class="tnm-dialog-content">
          <div class="tnm-dialog-title">
            <img src="@/assets/images/note.png" alt="注意" class="note-icon" />
            <p class="title-text">请注意！</p>
          </div>
          <div class="tnm-dialog-message">
            <p class="message-text">肿瘤患者抗肿瘤治疗前需明确TNM分期诊断</p>
          </div>
          <div class="tnm-dialog-actions">
            <span class="action-link" @click="handleSubmitTnmDiagnosis">提交TNM诊断</span>
            <span class="action-separator">|</span>
            <span class="action-link" @click="handleContinueSaveOrder">继续保存医嘱</span>
            <span class="action-separator">|</span>
            <span class="action-link return-link" @click="handleTnmDialogCancel">返回</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, reactive, onMounted, watch} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Plus} from '@element-plus/icons-vue'
import {apiServices} from '@/api'
import {useDictionaryStore} from '@/stores/dictionaryStore'
import {useRouter} from 'vue-router'
import {getAllUrlParams} from '@/utils/urlParams'
import PatientInfoPanel from './PatientInfoPanel.vue'

// 定义组件props
const props = defineProps({
  patient: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

// 定义组件事件
const emit = defineEmits(['count-updated'])

// 使用路由
const router = useRouter()

// 使用字典Store
const dictionaryStore = useDictionaryStore()

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const deleteLoading = ref(false)

// TNM分期诊断提示弹窗相关
const showTnmDialog = ref(false)
const pendingSaveData = ref(null)
const pendingSaveType = ref('') // 'edit' 或 'add'

// 字典数据相关状态
const medicalOrderTypeOptions = ref([])
const medicalOrderCategoryOptions = ref([])
const medicalOrderNameOptions = ref([])

const medicalOrderTypeLoading = ref(false)
const medicalOrderCategoryLoading = ref(false)
const medicalOrderNameLoading = ref(false)

// 医嘱信息列表
const medicalOrderList = ref([])

// 编辑状态
const editingIndex = ref(-1)
const isAdding = ref(false)

// 编辑表单
const editForm = reactive({
  orderSn: '',
  visitSn: '',
  orderType: '',
  orderClassCode: '',
  orderClassName: '',
  orderItemName: '',
  spec: '',
  frequencyCode: '',
  frequencyName: '',
  administrationRoute: '',
  dose: '',
  doseUnit: '',
  orderStartDatetime: '',
  orderEndDatetime: '',
  remark: '' // 添加备注字段
})

// 添加表单
const addForm = reactive({
  orderType: '',
  orderClassCode: '',
  orderClassName: '',
  orderItemName: '',
  spec: '',
  frequencyCode: '',
  frequencyName: '',
  administrationRoute: '',
  dose: '',
  doseUnit: '',
  orderStartDatetime: '',
  orderEndDatetime: '',
  remark: '' // 添加备注字段
})

// ========== 字典数据加载方法 ==========

/**
 * 加载字典数据
 */
const loadDictionaries = async () => {
  try {
    // 并行加载基础字典数据
    await Promise.all([
      loadMedicalOrderTypes(),
      loadMedicalOrderCategories(),
      loadMedicalOrderNames()
    ])
  } catch (error) {
    ElMessage.error('字典数据加载失败，请刷新页面重试')
  }
}

/**
 * 加载医嘱类型字典
 */
const loadMedicalOrderTypes = async () => {
  try {
    medicalOrderTypeLoading.value = true
    medicalOrderTypeOptions.value = await dictionaryStore.getMedicalOrderTypes()
  } catch (error) {
    medicalOrderTypeOptions.value = []
  } finally {
    medicalOrderTypeLoading.value = false
  }
}

/**
 * 加载医嘱类别字典
 */
const loadMedicalOrderCategories = async () => {
  try {
    medicalOrderCategoryLoading.value = true
    medicalOrderCategoryOptions.value = await dictionaryStore.getMedicalOrderCategories()
  } catch (error) {
    medicalOrderCategoryOptions.value = []
  } finally {
    medicalOrderCategoryLoading.value = false
  }
}

/**
 * 加载医嘱名称字典
 * @param {string} orderTypeName - 医嘱类型名称，用于获取对应的医嘱名称
 */
const loadMedicalOrderNames = async (orderTypeName = null) => {
  try {
    medicalOrderNameLoading.value = true
    // 根据医嘱类型名称动态获取对应的医嘱名称字典数据
    if (orderTypeName) {
      medicalOrderNameOptions.value = await dictionaryStore.getMedicalOrderNamesByType(orderTypeName)
    } else {
      medicalOrderNameOptions.value = await dictionaryStore.getMedicalOrderNames()
    }
  } catch (error) {
    medicalOrderNameOptions.value = []
  } finally {
    medicalOrderNameLoading.value = false
  }
}



// ========== 数据加载方法 ==========

/**
 * 加载医嘱信息列表
 */
const loadMedicalOrderList = async () => {
  if (!props.patient?.visitSn) {
    return
  }

  try {
    loading.value = true

    const data = await apiServices.medicalOrder.getList(props.patient.visitSn)
    medicalOrderList.value = data || []

  } catch (error) {
    ElMessage.error('加载医嘱信息列表失败: ' + error.message)
    medicalOrderList.value = []
  } finally {
    loading.value = false
  }
}

// ========== 工具方法 ==========

/**
 * 获取医嘱类型标签
 */
const getMedicalOrderTypeLabel = (value) => {
  const option = medicalOrderTypeOptions.value.find(item => item.value === value)
  return option ? option.label : value
}

/**
 * 获取医嘱类别标签
 */
const getMedicalOrderCategoryLabel = (value) => {
  const option = medicalOrderCategoryOptions.value.find(item => item.value === value)
  return option ? option.label : value
}

/**
 * 获取医嘱名称标签
 */
const getMedicalOrderNameLabel = (value) => {
  const option = medicalOrderNameOptions.value.find(item => item.value === value)
  return option ? option.label : value
}



/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return dateTime.replace('T', ' ').substring(0, 19)
}

/**
 * 判断是否为药品类医嘱
 * @param {string} orderClassCode - 医嘱类型代码
 * @returns {boolean} 是否为药品类医嘱
 */
const isDrugOrder = (orderClassCode) => {
  if (!orderClassCode) return false
  // 常见的药品类医嘱类型代码（可根据实际业务调整）
  const drugOrderTypes = ['药品', '西药', '中药', '中成药', '药物', 'DRUG', 'MEDICINE']
  return drugOrderTypes.some(type =>
    orderClassCode.includes(type) ||
    orderClassCode.toLowerCase().includes(type.toLowerCase())
  )
}

/**
 * 解析药品类医嘱的remark字段
 * @param {string} remark - 备注字段内容
 * @returns {Object} 解析结果
 */
const parseRemarkField = (remark) => {
  const result = {
    spec: '',           // 规格
    frequency: '',      // 频次
    usage: '',          // 用法
    dosage: '',         // 用量
    unit: '',           // 单位
    success: false      // 解析是否成功
  }

  if (!remark || typeof remark !== 'string') {
    return result
  }

  try {
    // 使用 "##" 作为分隔符进行解析
    const parts = remark.split('##').map(part => part.trim())

    // 按顺序提取信息：规格##频次##用法##用量##单位
    if (parts.length >= 5) {
      result.spec = parts[0] || ''
      result.frequency = parts[1] || ''
      result.usage = parts[2] || ''
      result.dosage = parts[3] || ''
      result.unit = parts[4] || ''
      result.success = true
    } else if (parts.length > 0) {
      // 部分解析，尽可能提取可用信息
      result.spec = parts[0] || ''
      if (parts.length > 1) result.frequency = parts[1] || ''
      if (parts.length > 2) result.usage = parts[2] || ''
      if (parts.length > 3) result.dosage = parts[3] || ''
      if (parts.length > 4) result.unit = parts[4] || ''
      result.success = parts.length > 1 // 至少有两个字段才算部分成功
    }
  } catch (error) {
    console.error('解析remark字段失败:', error)
  }

  return result
}

/**
 * 自动填充药品信息到表单
 * @param {Object} parsedData - 解析后的数据
 * @param {Object} targetForm - 目标表单对象
 */
const autoFillDrugInfo = (parsedData, targetForm) => {
  if (!parsedData.success) {
    return
  }

  try {
    // 直接填充解析出的文本值，不再匹配字典
    if (parsedData.spec) {
      targetForm.spec = parsedData.spec
    }

    if (parsedData.frequency) {
      targetForm.frequencyCode = parsedData.frequency
      targetForm.frequencyName = parsedData.frequency
    }

    if (parsedData.usage) {
      targetForm.administrationRoute = parsedData.usage
    }

    if (parsedData.dosage) {
      targetForm.dose = parsedData.dosage
    }

    if (parsedData.unit) {
      targetForm.doseUnit = parsedData.unit
    }

    console.log('药品信息自动填充完成:', parsedData)
  } catch (error) {
    console.error('自动填充药品信息失败:', error)
  }
}

/**
 * 清空编辑表单
 */
const clearEditForm = () => {
  Object.assign(editForm, {
    orderSn: '',
    visitSn: '',
    orderType: '',
    orderClassCode: '',
    orderClassName: '',
    orderItemName: '',
    spec: '',
    frequencyCode: '',
    frequencyName: '',
    administrationRoute: '',
    dose: '',
    doseUnit: '',
    orderStartDatetime: '',
    orderEndDatetime: '',
    remark: ''
  })
}

/**
 * 清空添加表单
 */
const clearAddForm = () => {
  Object.assign(addForm, {
    orderType: '',
    orderClassCode: '',
    orderClassName: '',
    orderItemName: '',
    spec: '',
    frequencyCode: '',
    frequencyName: '',
    administrationRoute: '',
    dose: '',
    doseUnit: '',
    orderStartDatetime: '',
    orderEndDatetime: '',
    remark: ''
  })
}

// ========== 事件处理方法 ==========

/**
 * 处理医嘱类别选择变化
 */
const handleOrderCategoryChange = async (value) => {
  const selectedOption = medicalOrderCategoryOptions.value.find(item => item.value === value)
  if (selectedOption) {
    editForm.orderClassName = selectedOption.label

    // 根据医嘱类型名称动态加载对应的医嘱名称字典数据
    await loadMedicalOrderNames(selectedOption.label)
  }

  // 清空医嘱名称选择，因为类型变化了
  editForm.orderItemName = ''
}



/**
 * 处理编辑
 */
const handleEdit = async (index) => {
  // 如果有正在编辑的行，先保存
  if (editingIndex.value !== -1) {
    await handleSave()
  }

  // 如果有正在添加的行，取消添加
  if (isAdding.value) {
    handleCancelAdd()
  }

  const item = medicalOrderList.value[index]
  editingIndex.value = index

  // 填充编辑表单
  Object.assign(editForm, {
    orderSn: item.orderSn || '',
    visitSn: item.visitSn || '',
    orderType: item.orderType || '',
    orderClassCode: item.orderClassCode || '',
    orderClassName: item.orderClassName || '',
    orderItemName: item.orderItemName || '',
    spec: item.spec || '',
    frequencyCode: item.frequencyCode || '',
    frequencyName: item.frequencyName || '',
    administrationRoute: item.administrationRoute || '',
    dose: item.dose || '',
    doseUnit: item.doseUnit || '',
    orderStartDatetime: item.orderStartDatetime || '',
    orderEndDatetime: item.orderEndDatetime || '',
    remark: item.remark || ''
  })

  // 如果是药品类医嘱且有remark字段，自动解析并填充
  if (isDrugOrder(item.orderClassCode) && item.remark) {
    const parsedData = parseRemarkField(item.remark)
    if (parsedData.success) {
      // 延迟执行自动填充，确保字典数据已加载
      setTimeout(() => {
        autoFillDrugInfo(parsedData, editForm)
      }, 100)
    }
  }
}

/**
 * 处理取消编辑
 */
const handleCancelEdit = () => {
  editingIndex.value = -1
  clearEditForm()
}

/**
 * 处理保存
 */
const handleSave = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法保存')
    return
  }

  // 验证必填字段
  if (!editForm.orderType || !editForm.orderClassCode || !editForm.orderItemName ||
      !editForm.spec || !editForm.frequencyCode || !editForm.administrationRoute ||
      !editForm.dose || !editForm.doseUnit) {
    ElMessage.error('请填写所有必填字段')
    return
  }

  // 构建保存数据（移到try块外面，这样catch块也能访问）
  const saveData = {
    orderSn: editForm.orderSn,
    visitSn: props.patient.visitSn,
    orderType: editForm.orderType,
    orderClassCode: editForm.orderClassCode,
    orderClassName: editForm.orderClassName,
    orderItemName: editForm.orderItemName,
    spec: editForm.spec || '',
    frequencyCode: editForm.frequencyCode || '',
    frequencyName: editForm.frequencyName || '',
    administrationRoute: editForm.administrationRoute || '',
    dose: editForm.dose || '',
    doseUnit: editForm.doseUnit || '',
    orderStartDatetime: editForm.orderStartDatetime || '',
    orderEndDatetime: editForm.orderEndDatetime || '',
    remark: editForm.remark || ''
  }

  try {
    saveLoading.value = true

    await apiServices.medicalOrder.update(editForm.orderSn, saveData)

    ElMessage.success('保存成功')

    // 重新加载列表
    await loadMedicalOrderList()

    // 退出编辑模式
    editingIndex.value = -1
    clearEditForm()

    // 触发数量更新事件
    emit('count-updated')
  } catch (error) {
    // 添加调试信息
    console.log('医嘱保存错误详情:', error)
    console.log('错误code:', error.code)
    console.log('错误message:', error.message)
    console.log('错误data:', error.data)

    // 检查是否是TNM分期诊断相关的错误（code为1002）
    if (error.code === 1002) {
      console.log('检测到code为1002，显示TNM分期诊断弹窗')
      // 保存当前数据，显示TNM分期诊断提示弹窗
      pendingSaveData.value = {
        orderSn: editForm.orderSn,
        saveData: saveData
      }
      pendingSaveType.value = 'edit'
      showTnmDialog.value = true
    } else {
      ElMessage.error('保存失败: ' + error.message)
    }
  } finally {
    saveLoading.value = false
  }
}

/**
 * 处理删除
 */
const handleDelete = async (item) => {
  if (!item.orderSn) {
    ElMessage.error('缺少必要的删除参数')
    return
  }

  try {
    await ElMessageBox.confirm(
        '确定要删除这条医嘱信息吗？删除后无法恢复。',
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
    )

    deleteLoading.value = true


    await apiServices.medicalOrder.delete(item.orderSn)

    ElMessage.success('删除成功')

    // 重新加载列表
    await loadMedicalOrderList()

    // 触发数量更新事件
    emit('count-updated')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  } finally {
    deleteLoading.value = false
  }
}

/**
 * 处理添加
 */
const handleAdd = () => {
  // 如果有正在编辑的行，先保存
  if (editingIndex.value !== -1) {
    handleSave()
  }

  isAdding.value = true
  clearAddForm()
}

/**
 * 处理取消添加
 */
const handleCancelAdd = () => {
  isAdding.value = false
  clearAddForm()
}

/**
 * 处理添加模式的医嘱类别选择变化
 */
const handleAddOrderCategoryChange = async (value) => {
  const selectedOption = medicalOrderCategoryOptions.value.find(item => item.value === value)
  if (selectedOption) {
    addForm.orderClassName = selectedOption.label

    // 根据医嘱类型名称动态加载对应的医嘱名称字典数据
    await loadMedicalOrderNames(selectedOption.label)
  }

  // 清空医嘱名称选择，因为类型变化了
  addForm.orderItemName = ''
}



/**
 * 处理编辑模式的备注字段失焦事件
 */
const handleRemarkBlur = () => {
  // 如果是药品类医嘱且有remark内容，尝试解析并自动填充
  if (isDrugOrder(editForm.orderClassName) && editForm.remark) {
    const parsedData = parseRemarkField(editForm.remark)
    if (parsedData.success) {
      autoFillDrugInfo(parsedData, editForm)
      ElMessage.success('已自动解析备注信息并填充到对应字段')
    } else if (editForm.remark.includes('##')) {
      ElMessage.warning('备注信息格式不完整，请检查是否按照"规格##频次##用法##用量##单位"的格式填写')
    }
  }
}

/**
 * 处理添加模式的备注字段失焦事件
 */
const handleAddRemarkBlur = () => {
  // 如果是药品类医嘱且有remark内容，尝试解析并自动填充
  if (isDrugOrder(addForm.orderClassName) && addForm.remark) {
    const parsedData = parseRemarkField(addForm.remark)
    if (parsedData.success) {
      autoFillDrugInfo(parsedData, addForm)
      ElMessage.success('已自动解析备注信息并填充到对应字段')
    } else if (addForm.remark.includes('##')) {
      ElMessage.warning('备注信息格式不完整，请检查是否按照"规格##频次##用法##用量##单位"的格式填写')
    }
  }
}

/**
 * 处理保存添加
 */
const handleSaveAdd = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法保存')
    return
  }

  // 验证必填字段
  if (!addForm.orderType || !addForm.orderClassCode || !addForm.orderItemName ||
      !addForm.spec || !addForm.frequencyCode || !addForm.administrationRoute ||
      !addForm.dose || !addForm.doseUnit) {
    ElMessage.error('请填写所有必填字段')
    return
  }

  // 构建保存数据（移到try块外面，这样catch块也能访问）
  const saveData = {
    visitSn: props.patient.visitSn,
    orderType: addForm.orderType,
    orderClassCode: addForm.orderClassCode,
    orderClassName: addForm.orderClassName,
    orderItemName: addForm.orderItemName,
    spec: addForm.spec || '',
    frequencyCode: addForm.frequencyCode || '',
    frequencyName: addForm.frequencyName || '',
    administrationRoute: addForm.administrationRoute || '',
    dose: addForm.dose || '',
    doseUnit: addForm.doseUnit || '',
    orderStartDatetime: addForm.orderStartDatetime || '',
    orderEndDatetime: addForm.orderEndDatetime || '',
    remark: addForm.remark || ''
  }

  try {
    saveLoading.value = true

    await apiServices.medicalOrder.save(saveData)

    ElMessage.success('添加成功')

    // 重新加载列表
    await loadMedicalOrderList()

    // 退出添加模式
    isAdding.value = false
    clearAddForm()

    // 触发数量更新事件
    emit('count-updated')
  } catch (error) {
    // 添加调试信息
    console.log('医嘱添加错误详情:', error)
    console.log('错误code:', error.code)
    console.log('错误message:', error.message)
    console.log('错误data:', error.data)

    // 检查是否是TNM分期诊断相关的错误（code为1002）
    if (error.code === 1002) {
      console.log('检测到code为1002，显示TNM分期诊断弹窗')
      // 保存当前数据，显示TNM分期诊断提示弹窗
      pendingSaveData.value = {
        saveData: saveData
      }
      pendingSaveType.value = 'add'
      showTnmDialog.value = true
    } else {
      ElMessage.error('添加失败: ' + error.message)
    }
  } finally {
    saveLoading.value = false
  }
}

// ========== TNM分期诊断弹窗处理方法 ==========

/**
 * 处理提交TNM诊断
 */
const handleSubmitTnmDiagnosis = () => {
  console.log('=== 用户点击提交TNM诊断 ===')
  console.log('当前患者信息:', props.patient)
  console.log('当前路由信息:', router.currentRoute.value)

  // 关闭弹窗
  showTnmDialog.value = false
  console.log('弹窗已关闭')

  // 跳转到TNM分期记录页面，并传递来源信息
  const currentParams = getAllUrlParams()
  console.log('当前URL参数:', currentParams)

  const routeParams = {
    name: 'PatientDetail',
    params: {
      visitSn: props.patient.visitSn
    },
    query: {
      ...currentParams,
      selectedMenuItem: 'tnm-staging',
      fromMedicalOrder: 'true' // 标记来源于医嘱页面
    }
  }

  console.log('准备跳转到TNM分期记录页面，路由参数:', routeParams)
  console.log('router对象:', router)

  // 检查是否已经在目标页面
  const currentRoute = router.currentRoute.value
  if (currentRoute.name === 'PatientDetail' &&
      currentRoute.params.visitSn === props.patient.visitSn) {
    console.log('在同一患者详情页面，使用replace更新URL参数')
    // 在同一页面内，使用replace更新URL参数来触发页面内容更新
    router.replace(routeParams).then(() => {
      console.log('页面参数更新成功')
    }).catch((error) => {
      console.error('页面参数更新失败:', error)
    })
  } else {
    console.log('跳转到不同页面，使用push')
    router.push(routeParams).then(() => {
      console.log('路由跳转成功')
    }).catch((error) => {
      console.error('路由跳转失败:', error)
    })
  }

  // 清空待保存数据
  pendingSaveData.value = null
  pendingSaveType.value = ''
  console.log('=== 提交TNM诊断处理完成 ===')
}

/**
 * 处理继续保存医嘱
 */
const handleContinueSaveOrder = async () => {
  if (!pendingSaveData.value) {
    ElMessage.error('没有待保存的数据')
    return
  }

  try {
    saveLoading.value = true

    if (pendingSaveType.value === 'edit') {
      // 编辑模式：强制保存
      await apiServices.medicalOrder.update(pendingSaveData.value.orderSn, pendingSaveData.value.saveData, { force: true })

      ElMessage.success('保存成功')

      // 重新加载列表
      await loadMedicalOrderList()

      // 退出编辑模式
      editingIndex.value = -1
      clearEditForm()

      // 触发数量更新事件
      emit('count-updated')
    } else if (pendingSaveType.value === 'add') {
      // 添加模式：强制保存
      await apiServices.medicalOrder.save(pendingSaveData.value.saveData, { force: true })

      ElMessage.success('添加成功')

      // 重新加载列表
      await loadMedicalOrderList()

      // 退出添加模式
      isAdding.value = false
      clearAddForm()

      // 触发数量更新事件
      emit('count-updated')
    }
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saveLoading.value = false

    // 关闭弹窗并清空待保存数据
    showTnmDialog.value = false
    pendingSaveData.value = null
    pendingSaveType.value = ''
  }
}

/**
 * 处理取消TNM分期诊断弹窗
 */
const handleTnmDialogCancel = () => {
  // 关闭弹窗
  showTnmDialog.value = false

  // 清空待保存数据
  pendingSaveData.value = null
  pendingSaveType.value = ''
}

// 监听患者变化，重新加载数据
watch(() => props.patient?.visitSn, (newVisitSn) => {
  if (newVisitSn) {
    loadMedicalOrderList()
  }
}, {immediate: false})

// 组件挂载时加载数据
onMounted(async () => {
  await loadDictionaries()
  if (props.patient?.visitSn) {
    await loadMedicalOrderList()
  }
})
</script>

<style scoped>
/* 整体容器 */
.medical-order-info-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 180px);
  gap: 20px;
}

/* 右侧医嘱信息内容区域 */
.medical-order-content-area {
  flex: 1;
  height: 100%;
  background: #FFFFFF;
  border-radius: 0 12px 12px 0;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 加载容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

/* 内容包装器 */
.content-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 页面标题 */
.page-title {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.title-text {
  width: 113px;
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #172B4D;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin: 0;
}

/* 医嘱信息表格容器 */
.medical-order-table-container {
  flex: 1;
  overflow-x: auto;
  overflow-y: auto;
  width: 100%;
  position: relative;
}

/* 医嘱信息表格 */
.medical-order-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  min-width: 1750px; /* 增加最小宽度以适应新增的备注列 */
  position: relative;
}

/* 表头样式 */
.table-header {
  height: 44px;
  background: #F4F5F7;
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #EBECF0;
}

.header-cell {
  padding: 12px 8px;
  text-align: left;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #172B4D;
  line-height: 20px;
  border: 1px solid #EBECF0;
  white-space: nowrap;
}

/* 必填字段标识样式 */
.required-field {
  color: #FF4D4F;
  margin-right: 2px;
  font-weight: 500;
}
/* 医嘱类型 */
.header-cell:nth-child(1) {
  width: 130px;
}

/* 医嘱类别 */
.header-cell:nth-child(2) {
  width: 160px;
}

/* 医嘱名称 */
.header-cell:nth-child(3) {
  width: 160px;
}

/* 药品规格 */
.header-cell:nth-child(4) {
  width: 130px;
}

/* 频次 */
.header-cell:nth-child(5) {
  width: 140px;
}

/* 用法 */
.header-cell:nth-child(6) {
  width: 120px;
}

/* 用量 */
.header-cell:nth-child(7) {
  width: 100px;
}

/* 单位 */
.header-cell:nth-child(8) {
  width: 100px;
}

/* 备注 */
.header-cell:nth-child(9) {
  width: 150px;
}

/* 开始时间 */
.header-cell:nth-child(10) {
  width: 180px;
}

/* 结束时间 */
.header-cell:nth-child(11) {
  width: 180px;
}

/* 操作 */
.header-cell:nth-child(12) {
  width: 120px;
  position: sticky;
  right: 0;
  background: #F4F5F7;
  z-index: 10;
}



/* 表格行样式 */
.table-row {
  height: 44px;
  background: #FFFFFF;
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #EBECF0;
}

.table-row:hover {
  background: #F4F5F7;
}

.add-row {
  background: #FAFCFF !important;
  width: 100%;
}

.add-row:hover {
  background: #FAFCFF !important;
}

/* 表格单元格样式 */
.table-cell {
  padding: 6px 4px;
  text-align: left;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #172B4D;
  line-height: 20px;
  border: 1px solid #EBECF0;
  vertical-align: middle;
  position: relative;
}

/* 操作列固定样式 */
.table-cell:last-child {
  position: sticky;
  right: 0;
  background: #FFFFFF;
  z-index: 9;
}

.table-row:hover .table-cell:last-child {
  background: #F4F5F7;
}

.add-row .table-cell:last-child {
  background: #FAFCFF !important;
}

/* 单元格内容不是输入框时的样式 */
.table-cell > span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

/* 输入框和选择框样式 */
.cell-input {
  width: calc(100% - 8px);
  height: 32px;
  border-radius: 0px;
  max-width: 100%;
}

.cell-input :deep(.el-input__wrapper),
.cell-input :deep(.el-select__wrapper) {
  height: 32px;
  border-radius: 0px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  box-shadow: none;
  max-width: 100%;
}

/* 日期选择器特殊样式 */
.cell-input :deep(.el-date-editor) {
  width: 100% !important;
  max-width: 100% !important;
}

.cell-input :deep(.el-date-editor .el-input__wrapper) {
  width: 100% !important;
  max-width: 100% !important;
}

.cell-input :deep(.el-input__inner) {
  height: 30px;
  border: none;
  border-radius: 0px;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  box-shadow: none;
}

.cell-input :deep(.el-input__wrapper:focus),
.cell-input :deep(.el-select__wrapper.is-focused) {
  border-color: #EBECF0 !important;
  box-shadow: none !important;
}

.cell-input :deep(.el-input__inner:focus) {
  outline: none !important;
  border-color: #EBECF0 !important;
  box-shadow: none !important;
}

.cell-input :deep(.el-select__input:focus) {
  outline: none !important;
  border-color: #EBECF0 !important;
  box-shadow: none !important;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.action-buttons .el-button {
  height: 28px;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  min-width: auto;
}

/* 添加按钮容器样式 */
.add-button-container {
  position: sticky;
  left: 0;
  width: 100%;
  /* 移除 min-width 属性 */
  padding: 8px 12px;
  background: #FAFCFF;
  border-top: 1px solid #EBECF0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-left: 1px solid #EBECF0;
  border-right: 1px solid #EBECF0;
  border-bottom: 1px solid #EBECF0;
  z-index: 8; /* 确保按钮在滚动时保持可见 */
}

/* 添加按钮样式 - 参考日常病程记录 */
.add-record-btn {
  width: 80px;
  height: 32px;
  background: transparent;
  border: none;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #1678FF;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  cursor: pointer;
}

.add-record-btn:hover {
  color: #1678FF;
  background: transparent;
  border: none;
}

.add-record-btn:focus {
  color: #1678FF;
  outline: none;
  background: transparent;
  border: none;
}

/* 新建模式时的激活状态 */
.add-record-btn.active {
  color: #1678FF;
}

/* ========== TNM分期诊断提示弹窗样式 ========== */

/* 弹窗遮罩层 */
.tnm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

/* 弹窗容器 */
.tnm-dialog-container {
  background: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

/* 弹窗内容 */
.tnm-dialog-content {
  padding: 24px;
  background-color: rgba(255, 251, 230, 1);
  border-color: rgba(255, 229, 143, 1);
}

/* 弹窗标题 */
.tnm-dialog-title {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tnm-dialog-title .note-icon {
  width: 24px;
  height: 26px;
  flex-shrink: 0;
  display: block;
}

.tnm-dialog-title .title-text {
  font-family: 'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-size: 18px;
  line-height: 20px; /* 调整行高与图标高度一致 */
  color: rgba(0, 0, 0, 0.647);
  margin: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
}

/* 弹窗消息 */
.tnm-dialog-message {
  margin-bottom: 5px;
  margin-left: 32px; /* 图片宽度(20px) + 间距(8px) = 28px，与"请注意"文字对齐 */
}

.tnm-dialog-message .message-text {
  font-family: 'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-size: 14px;
  line-height: 26px;
  color: rgba(0, 0, 0, 0.447);
  margin: 0;
}

/* 弹窗操作按钮区域 */
.tnm-dialog-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-left: 32px; /* 图片宽度(20px) + 间距(8px) = 28px，与"请注意"文字对齐 */
}

/* 操作链接 */
.action-link {
  font-family: 'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-size: 14px;
  line-height: 26px;
  color: #1890FF;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease;
}

.action-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 返回链接特殊样式 */
.action-link.return-link {
  color: rgba(0, 178, 255, 0.647);
}

.action-link.return-link:hover {
  color: rgba(0, 178, 255, 0.8);
}

/* 分隔符 */
.action-separator {
  font-family: 'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-size: 14px;
  line-height: 26px;
  color: #CCCCCC;
  margin: 0 4px;
}
</style>

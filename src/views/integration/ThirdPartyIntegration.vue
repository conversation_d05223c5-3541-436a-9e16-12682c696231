<!--
  第三方系统集成管理页面
  用于配置和管理与外部系统的集成
-->
<template>
  <div class="third-party-integration">
    <div class="page-header">
      <h1>第三方系统集成</h1>
      <p>管理与外部系统的集成配置和连接状态</p>
    </div>

    <!-- 系统状态概览 -->
    <el-row :gutter="20" class="status-overview">
      <el-col :span="8">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon" :class="{ active: integrationStatus.yingchunhua }">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="status-info">
              <h3>迎春花质控系统</h3>
              <p :class="integrationStatus.yingchunhua ? 'connected' : 'disconnected'">
                {{ integrationStatus.yingchunhua ? '已连接' : '未连接' }}
              </p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon" :class="{ active: integrationStatus.drg }">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="status-info">
              <h3>DRG系统</h3>
              <p :class="integrationStatus.drg ? 'connected' : 'disconnected'">
                {{ integrationStatus.drg ? '已连接' : '未连接' }}
              </p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon" :class="{ active: browserCompatible }">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="status-info">
              <h3>浏览器兼容性</h3>
              <p :class="browserCompatible ? 'connected' : 'disconnected'">
                {{ browserCompatible ? '兼容' : '不兼容' }}
              </p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 集成系统配置 -->
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>集成系统配置</span>
          <el-button type="primary" @click="testAllConnections">
            <el-icon><Refresh /></el-icon>
            测试所有连接
          </el-button>
        </div>
      </template>

      <el-tabs v-model="activeTab" type="card">
        <!-- 迎春花质控系统 -->
        <el-tab-pane label="迎春花质控系统" name="yingchunhua">
          <div class="system-config">
            <el-form :model="yingchunhuaForm" label-width="120px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="SDK地址">
                    <el-input v-model="yingchunhuaForm.sdkUrl" placeholder="请输入SDK地址" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="链接类型">
                    <el-select v-model="yingchunhuaForm.linkType">
                      <el-option label="Web版本" value="2" />
                      <el-option label="App版本" value="1" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="应用密钥">
                    <el-input v-model="yingchunhuaForm.appKey" placeholder="请输入AppKey" show-password />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="应用秘密密钥">
                    <el-input v-model="yingchunhuaForm.appSecretKey" placeholder="请输入AppSecretKey" show-password />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="科室ID">
                    <el-input v-model="yingchunhuaForm.deptId" placeholder="请输入科室ID" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="医生ID">
                    <el-input v-model="yingchunhuaForm.doctorId" placeholder="请输入医生ID" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="质控目标">
                <el-radio-group v-model="yingchunhuaForm.qualityTarget">
                  <el-radio label="1">临床</el-radio>
                  <el-radio label="2">病理</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="initializeYingchunhua" :loading="loading.yingchunhua">
                  初始化连接
                </el-button>
                <el-button @click="testYingchunhuaConnection" :loading="loading.yingchunhuaTest">
                  测试连接
                </el-button>
                <el-button @click="resetYingchunhuaConfig">重置配置</el-button>
              </el-form-item>
            </el-form>

            <!-- 功能模块状态 -->
            <div class="modules-status">
              <h4>功能模块</h4>
              <el-row :gutter="16">
                <el-col :span="6" v-for="module in yingchunhuaModules" :key="module.id">
                  <el-card class="module-card">
                    <div class="module-info">
                      <h5>{{ module.name }}</h5>
                      <p>{{ module.patientTypes.join('、') }}</p>
                      <el-tag v-if="module.note" size="small" type="info">{{ module.note }}</el-tag>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-tab-pane>

        <!-- DRG系统 -->
        <el-tab-pane label="DRG系统" name="drg">
          <div class="system-config">
            <el-form :model="drgForm" label-width="120px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="API地址">
                    <el-input v-model="drgForm.apiUrl" placeholder="请输入API地址" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="应用密钥">
                    <el-input v-model="drgForm.appKey" placeholder="请输入AppKey" show-password />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="应用秘密">
                <el-input v-model="drgForm.appSecret" placeholder="请输入AppSecret" show-password />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="initializeDrg" :loading="loading.drg">
                  初始化连接
                </el-button>
                <el-button @click="testDrgConnection" :loading="loading.drgTest">
                  测试连接
                </el-button>
                <el-button @click="resetDrgConfig">重置配置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 集成日志 -->
        <el-tab-pane label="集成日志" name="logs">
          <div class="logs-container">
            <div class="logs-header">
              <el-button @click="clearLogs" size="small">清空日志</el-button>
              <el-button @click="exportLogs" size="small">导出日志</el-button>
            </div>
            <div class="logs-content">
              <div v-for="(log, index) in integrationLogs" :key="index" 
                   class="log-item" :class="log.type">
                <span class="log-time">{{ log.timestamp }}</span>
                <span class="log-system">{{ log.system }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 患者数据测试 -->
    <el-card class="test-card">
      <template #header>
        <span>患者数据测试</span>
      </template>
      
      <el-form :model="testPatientForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="患者ID">
              <el-input v-model="testPatientForm.patientId" placeholder="请输入患者ID" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="就诊ID">
              <el-input v-model="testPatientForm.visitSn" placeholder="请输入就诊ID" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="就诊类型">
              <el-select v-model="testPatientForm.visitType">
                <el-option label="住院" value="住院" />
                <el-option label="门诊" value="门诊" />
                <el-option label="体检" value="体检" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button type="primary" @click="testPatientDataSend" :loading="loading.patientTest">
            测试发送患者数据
          </el-button>
          <el-button @click="generateTestData">生成测试数据</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Connection, DataAnalysis, Monitor, Refresh } from '@element-plus/icons-vue'
import thirdPartyIntegration from '@/services/thirdPartyIntegration'
import { getCurrentConfig, integrationSystems, browserCompatibility } from '@/config/thirdPartyConfig'

// 响应式数据
const activeTab = ref('yingchunhua')
const integrationStatus = reactive({
  yingchunhua: false,
  drg: false
})

const loading = reactive({
  yingchunhua: false,
  yingchunhuaTest: false,
  drg: false,
  drgTest: false,
  patientTest: false
})

// 表单数据
const yingchunhuaForm = reactive({
  sdkUrl: '',
  appKey: '',
  appSecretKey: '',
  deptId: '',
  doctorId: '',
  qualityTarget: '2',
  linkType: '2'
})

const drgForm = reactive({
  apiUrl: '',
  appKey: '',
  appSecret: ''
})

const testPatientForm = reactive({
  patientId: '',
  visitSn: '',
  visitType: '住院'
})

// 集成日志
const integrationLogs = ref([])

// 计算属性
const browserCompatible = computed(() => {
  const compatibility = browserCompatibility.checkCompatibility()
  return compatibility.isSupported
})

const yingchunhuaModules = computed(() => {
  const system = integrationSystems.find(s => s.id === 'yingchunhua')
  return system?.modules || []
})

// 方法
const addLog = (system, message, type = 'info') => {
  integrationLogs.value.unshift({
    timestamp: new Date().toLocaleString(),
    system,
    message,
    type
  })
  
  // 限制日志数量
  if (integrationLogs.value.length > 100) {
    integrationLogs.value = integrationLogs.value.slice(0, 100)
  }
}

const initializeYingchunhua = async () => {
  loading.yingchunhua = true
  try {
    await thirdPartyIntegration.initialize(yingchunhuaForm)
    integrationStatus.yingchunhua = true
    addLog('迎春花', '系统初始化成功', 'success')
    ElMessage.success('迎春花质控系统初始化成功')
  } catch (error) {
    addLog('迎春花', `初始化失败: ${error.message}`, 'error')
    ElMessage.error('初始化失败: ' + error.message)
  } finally {
    loading.yingchunhua = false
  }
}

const testYingchunhuaConnection = async () => {
  loading.yingchunhuaTest = true
  try {
    const status = thirdPartyIntegration.getStatus()
    if (status.isInitialized) {
      addLog('迎春花', '连接测试成功', 'success')
      ElMessage.success('连接测试成功')
    } else {
      throw new Error('SDK未初始化')
    }
  } catch (error) {
    addLog('迎春花', `连接测试失败: ${error.message}`, 'error')
    ElMessage.error('连接测试失败: ' + error.message)
  } finally {
    loading.yingchunhuaTest = false
  }
}

const resetYingchunhuaConfig = () => {
  const config = getCurrentConfig('yingchunhua')
  Object.assign(yingchunhuaForm, config)
  addLog('迎春花', '配置已重置为默认值', 'info')
}

const initializeDrg = async () => {
  loading.drg = true
  try {
    // DRG系统初始化逻辑
    integrationStatus.drg = true
    addLog('DRG', '系统初始化成功', 'success')
    ElMessage.success('DRG系统初始化成功')
  } catch (error) {
    addLog('DRG', `初始化失败: ${error.message}`, 'error')
    ElMessage.error('初始化失败: ' + error.message)
  } finally {
    loading.drg = false
  }
}

const testDrgConnection = async () => {
  loading.drgTest = true
  try {
    // DRG连接测试逻辑
    addLog('DRG', '连接测试成功', 'success')
    ElMessage.success('DRG连接测试成功')
  } catch (error) {
    addLog('DRG', `连接测试失败: ${error.message}`, 'error')
    ElMessage.error('连接测试失败: ' + error.message)
  } finally {
    loading.drgTest = false
  }
}

const resetDrgConfig = () => {
  const config = getCurrentConfig('drg')
  Object.assign(drgForm, config)
  addLog('DRG', '配置已重置为默认值', 'info')
}

const testAllConnections = async () => {
  await Promise.all([
    testYingchunhuaConnection(),
    testDrgConnection()
  ])
}

const testPatientDataSend = async () => {
  loading.patientTest = true
  try {
    // 构造测试患者数据
    const testData = {
      patient: {
        patientId: testPatientForm.patientId,
        name: '测试患者',
        gender: '男',
        dateOfBirth: '1980-01-01',
        // ... 其他必要字段
      },
      visit: {
        visitSn: testPatientForm.visitSn,
        visitType: testPatientForm.visitType,
        // ... 其他必要字段
      }
    }
    
    await thirdPartyIntegration.sendPatientData(testData)
    addLog('测试', '患者数据发送成功', 'success')
    ElMessage.success('患者数据发送成功')
  } catch (error) {
    addLog('测试', `患者数据发送失败: ${error.message}`, 'error')
    ElMessage.error('发送失败: ' + error.message)
  } finally {
    loading.patientTest = false
  }
}

const generateTestData = () => {
  testPatientForm.patientId = 'TEST_' + Date.now()
  testPatientForm.visitSn = testPatientForm.patientId + '|001|1住院'
  ElMessage.success('测试数据已生成')
}

const clearLogs = () => {
  integrationLogs.value = []
  ElMessage.success('日志已清空')
}

const exportLogs = () => {
  const logText = integrationLogs.value
    .map(log => `${log.timestamp} [${log.system}] ${log.message}`)
    .join('\n')
  
  const blob = new Blob([logText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `integration_logs_${new Date().toISOString().slice(0, 10)}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

// 生命周期
onMounted(() => {
  // 加载默认配置
  resetYingchunhuaConfig()
  resetDrgConfig()
  
  // 检查浏览器兼容性
  const compatibility = browserCompatibility.checkCompatibility()
  addLog('系统', `浏览器检测: ${compatibility.detectedBrowser}`, 
        compatibility.isSupported ? 'success' : 'warning')
})
</script>

<style scoped>
.third-party-integration {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 500;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.status-overview {
  margin-bottom: 24px;
}

.status-card {
  height: 100px;
}

.status-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #909399;
  margin-right: 16px;
  transition: all 0.3s;
}

.status-icon.active {
  background: #e1f3d8;
  color: #67c23a;
}

.status-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.status-info p {
  margin: 0;
  font-size: 14px;
}

.status-info p.connected {
  color: #67c23a;
}

.status-info p.disconnected {
  color: #f56c6c;
}

.config-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.system-config {
  padding: 20px 0;
}

.modules-status {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

.modules-status h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.module-card {
  margin-bottom: 16px;
  height: 80px;
}

.module-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.module-info h5 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.module-info p {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #909399;
}

.test-card {
  margin-bottom: 24px;
}

.logs-container {
  max-height: 400px;
  overflow-y: auto;
}

.logs-header {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.logs-content {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  display: flex;
  padding: 4px 0;
  border-bottom: 1px solid #f5f7fa;
}

.log-item.success {
  color: #67c23a;
}

.log-item.error {
  color: #f56c6c;
}

.log-item.warning {
  color: #e6a23c;
}

.log-item.info {
  color: #909399;
}

.log-time {
  width: 140px;
  flex-shrink: 0;
  margin-right: 12px;
}

.log-system {
  width: 80px;
  flex-shrink: 0;
  margin-right: 12px;
  font-weight: 500;
}

.log-message {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .third-party-integration {
    padding: 12px;
  }

  .status-overview .el-col {
    margin-bottom: 12px;
  }

  .status-item {
    flex-direction: column;
    text-align: center;
  }

  .status-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }
}
</style>

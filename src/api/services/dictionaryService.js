/**
 * 字典数据相关API服务
 */

import request from '@/utils/request'
import { dictionaryApi } from '@/api/endpoints'

/**
 * 字典服务类
 */
export class DictionaryService {
  // 防重复调用的Promise缓存
  static promiseCache = new Map()

  /**
   * 根据字典类型获取字典数据
   * @param {string} dictType - 字典类型
   * @returns {Promise<Array>} 字典数据列表
   */
  static async getByType(dictType) {
    if (!dictType) {
      throw new Error('dictType参数不能为空')
    }

    const cacheKey = `dict_${dictType}`

    // 如果正在请求相同dictType的数据，返回同一个Promise避免重复请求
    if (this.promiseCache.has(cacheKey)) {
      console.log(`字典数据 ${dictType} 请求进行中，等待现有请求完成...`)
      return await this.promiseCache.get(cacheKey)
    }

    // 创建新的请求Promise
    const promise = (async () => {
      try {
        console.log(`正在获取字典数据: ${dictType}`)
        
        const response = await request.get(dictionaryApi.getByType(dictType))
        
        // 检查响应格式
        if (response.code !== 0) {
          throw new Error(response.msg || '获取字典数据失败')
        }

        const dictData = response.data || []
        
        // 数据映射：将API数据格式转换为前端所需格式
        const mappedData = dictData
          .filter(item => item.status === 0) // 只返回启用状态的数据
          .sort((a, b) => (a.sort || 0) - (b.sort || 0)) // 按sort字段排序
          .map(item => ({
            id: item.id,
            label: item.label, // 显示名称
            value: item.value, // 实际值
            code: item.value,  // 兼容性字段
            name: item.label,  // 兼容性字段
            dictType: item.dictType,
            sort: item.sort || 0,
            status: item.status,
            remark: item.remark
          }))

        console.log(`字典数据 ${dictType} 获取成功:`, mappedData)
        return mappedData

      } catch (error) {
        console.error(`获取字典数据 ${dictType} 失败:`, error)
        throw new Error(error.message || `获取字典数据失败`)
      } finally {
        // 清除Promise缓存，允许下次重新请求
        this.promiseCache.delete(cacheKey)
      }
    })()

    this.promiseCache.set(cacheKey, promise)
    return await promise
  }

  /**
   * 分页获取诊断名称字典
   * @param {object} params - 查询参数
   * @param {number} params.pageNum - 页码
   * @param {number} params.pageSize - 每页条数
   * @param {string} params.label - 搜索关键词
   * @returns {Promise<object>} 分页数据，包含 records 和 total
   */
  static async getDiagnosisNamesByPage(params) {
    try {
      console.log('正在分页获取诊断名称字典:', params)

      const response = await request.get(dictionaryApi.getPage, {
        params: {
          ...params,
          dictType: 'diagnostic_name'
        }
      })

      if (response.code !== 0) {
        throw new Error(response.msg || '分页获取诊断名称失败')
      }

      console.log('分页获取诊断名称成功:', response.data)
      return response.data
    } catch (error) {
      console.error('分页获取诊断名称失败:', error)
      throw new Error(error.message || '分页获取诊断名称失败')
    }
  }

  /**
   * 获取邀请科室字典
   * @returns {Promise<Array>} 邀请科室列表
   */
  static async getInviteDepartments() {
    return await this.getByType('invite_department')
  }

  /**
   * 获取诊断名称字典
   * @returns {Promise<Array>} 诊断名称列表
   */
  static async getDiagnosisNames() {
    return await this.getByType('diagnostic_name')
  }

  /**
   * 获取诊断类型字典
   * @returns {Promise<Array>} 诊断类型列表
   */
  static async getDiagnosisTypes() {
    return await this.getByType('diag_type')
  }

  /**
   * 获取是否主要诊断字典
   * @returns {Promise<Array>} 是否主要诊断列表
   */
  static async getYesOrNo() {
    return await this.getByType('yes_or_no')
  }

  /**
   * 清除所有Promise缓存
   */
  static clearCache() {
    this.promiseCache.clear()
    console.log('字典服务Promise缓存已清除')
  }
}

// 导出默认实例
export default DictionaryService

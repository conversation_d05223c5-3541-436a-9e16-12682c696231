# YYHIS Web 部署文档

## 环境要求

- Node.js 20.19.0
- npm 或 yarn

## 快速部署

### 1. 安装依赖
```bash
npm install
```

### 2. 构建项目
```bash
# 开发环境构建
npm run build:dev

# 测试环境构建  
npm run build:test

# 生产环境构建
npm run build:prod
```

### 3. 部署静态文件
构建完成后，将 `dist` 目录下的所有文件部署到 Web 服务器即可。

## Web 服务器配置

### Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;
    
    # SPA 路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API 代理（可选）
    location /api/ {
        proxy_pass http://backend-server:6596/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```


## 环境变量配置

根据部署环境创建对应的环境变量文件：

- `.env.development` - 开发环境
- `.env.test` - 测试环境  
- `.env.production` - 生产环境

主要配置项：
```
VITE_BACKEND_URL=http://your-backend-server:6596
```


# 医嘱信息动态处理功能实现说明

## 功能概述

根据用户需求，在 MedicalOrderInfo.vue 组件中实现了医嘱信息的动态处理功能：

1. **医嘱类型识别与字典数据获取**：根据医嘱类型动态获取对应的医嘱名称字典数据
2. **药品类医嘱的 remark 字段解析**：解析备注字段并自动填充药品相关信息
3. **简化药品字段处理**：药品相关字段不再使用字典，直接从备注解析

## 核心实现

### 1. 医嘱类型识别与字典数据获取

#### 实现方式
- 在医嘱类型选择变化时，以医嘱类型名称作为字典类型请求对应的医嘱名称数据
- 修改了 `dictionaryStore` 的 `getMedicalOrderNamesByType` 方法

```javascript
// 直接以医嘱类型名称作为字典类型请求数据
const getMedicalOrderNamesByType = async (orderTypeName, forceRefresh = false) => {
  // 例如：orderTypeName = "检查检验类医嘱"，就请求字典类型为"检查检验类医嘱"的数据
  return await getDictionary(orderTypeName, forceRefresh)
}
```

#### 使用示例
- 选择"检查检验类医嘱" → 请求字典类型为"检查检验类医嘱"的医嘱名称数据
- 选择"药品类医嘱" → 请求字典类型为"药品类医嘱"的医嘱名称数据

### 2. 药品类医嘱的 remark 字段解析

#### 解析格式
使用 "##" 作为分隔符，按以下顺序解析：
```
规格##频次##用法##用量##单位
```

#### 示例
```
阿司匹林肠溶片100mg##每日一次##口服##1片##片
```

#### 药品类医嘱识别
通过 `isDrugOrder` 函数识别，支持以下类型：
- 药品、西药、中药、中成药、药物
- DRUG、MEDICINE

### 3. 药品字段处理简化

#### 移除的功能
- ❌ 删除了药品规格、频次、用法、用量、单位的字典加载
- ❌ 删除了对应的选择框组件
- ❌ 删除了字典匹配逻辑

#### 新的处理方式
- ✅ 药品类医嘱：字段显示为只读输入框，从备注解析填充
- ✅ 非药品类医嘱：字段显示为可编辑输入框，用户手动输入

```javascript
// 药品类医嘱 - 只读输入框
<el-input
    v-if="isDrugOrder(editForm.orderClassName)"
    v-model="editForm.spec"
    placeholder="规格信息（从备注解析）"
    readonly
/>

// 非药品类医嘱 - 可编辑输入框
<el-input
    v-else
    v-model="editForm.spec"
    placeholder="请输入规格"
/>
```

## 使用流程

### 1. 选择医嘱类型
1. 用户选择医嘱类型（如"检查检验类医嘱"）
2. 系统以该名称为字典类型请求对应的医嘱名称数据
3. 医嘱名称下拉框更新为对应类型的选项

### 2. 药品类医嘱处理
1. 用户选择药品类医嘱类型
2. 在备注字段输入格式化信息：`规格##频次##用法##用量##单位`
3. 失焦时自动解析并填充到对应字段
4. 药品相关字段变为只读状态，显示解析结果

### 3. 非药品类医嘱处理
1. 用户选择非药品类医嘱类型
2. 药品相关字段显示为可编辑输入框
3. 用户可以手动输入相关信息

## 代码优化

### 清理的代码
1. **字典数据状态**：删除了药品相关的字典选项和加载状态
2. **字典加载方法**：删除了5个药品字典加载方法
3. **标签获取方法**：删除了5个药品字典标签获取方法
4. **事件处理**：删除了频次选择变化处理方法

### 保留的核心功能
1. **解析功能**：`parseRemarkField` - 解析备注字段
2. **填充功能**：`autoFillDrugInfo` - 自动填充药品信息
3. **识别功能**：`isDrugOrder` - 识别药品类医嘱
4. **动态加载**：`loadMedicalOrderNames` - 动态加载医嘱名称

## 配置说明

### 药品类医嘱识别配置
```javascript
const isDrugOrder = (orderClassName) => {
  const drugOrderTypes = ['药品', '西药', '中药', '中成药', '药物', 'DRUG', 'MEDICINE']
  return drugOrderTypes.some(type => 
    orderClassName && orderClassName.includes(type)
  )
}
```

### 字典类型映射
- 医嘱类型名称直接作为字典类型使用
- 例如："检查检验类医嘱" → 字典类型："检查检验类医嘱"

## 注意事项

1. **备注格式**：药品类医嘱的备注必须严格按照 "规格##频次##用法##用量##单位" 格式
2. **字典依赖**：医嘱名称的动态加载依赖于后端提供对应的字典类型数据
3. **兼容性**：新功能完全兼容现有的医嘱信息录入流程
4. **性能优化**：移除了不必要的字典加载，提高了页面加载速度

## 错误处理

1. **解析失败**：显示格式提示信息
2. **字典加载失败**：回退到默认医嘱名称字典
3. **网络错误**：显示友好的错误提示

这个实现完全符合用户的需求，提供了一个简洁高效的医嘱信息动态处理解决方案。

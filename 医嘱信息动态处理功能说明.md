# 医嘱信息动态处理功能说明

## 功能概述

在 MedicalOrderInfo.vue 组件中实现了医嘱信息的动态处理功能，包括：

1. **医嘱类型识别与字典数据获取**
2. **药品类医嘱的 remark 字段解析**
3. **自动填充选择框**

## 功能详细说明

### 1. 医嘱类型识别与字典数据获取

#### 实现方式
- 在医嘱类型（orderClassCode）选择变化时，动态获取对应的医嘱名称字典数据
- 扩展了 `dictionaryStore` 添加了 `getMedicalOrderNamesByType` 方法
- 支持根据医嘱类型过滤医嘱名称选项

#### 使用方法
```javascript
// 根据医嘱类型获取对应的医嘱名称
await loadMedicalOrderNames(orderClassCode)
```

#### 触发时机
- 编辑模式：`handleOrderCategoryChange` 方法
- 添加模式：`handleAddOrderCategoryChange` 方法

### 2. 药品类医嘱的 remark 字段解析

#### 解析格式
使用 "##" 作为分隔符，按以下顺序解析：
```
规格##频次##用法##用量##单位
```

#### 示例
```
10mg##每日三次##口服##1片##片
```

#### 药品类医嘱识别
通过 `isDrugOrder` 函数识别，支持以下类型：
- 药品
- 西药
- 中药
- 中成药
- 药物
- DRUG
- MEDICINE

#### 解析方法
```javascript
const parseRemarkField = (remark) => {
  // 返回解析结果对象
  return {
    spec: '',           // 规格
    frequency: '',      // 频次
    usage: '',          // 用法
    dosage: '',         // 用量
    unit: '',           // 单位
    success: false      // 解析是否成功
  }
}
```

### 3. 自动填充选择框

#### 填充逻辑
- 解析 remark 字段后，自动匹配字典数据
- 将匹配的值填充到对应的表单字段
- 支持按 label 或 value 进行匹配

#### 填充字段映射
- `spec` → `editForm.spec` / `addForm.spec`
- `frequency` → `editForm.frequencyCode` / `addForm.frequencyCode`
- `usage` → `editForm.administrationRoute` / `addForm.administrationRoute`
- `dosage` → `editForm.dose` / `addForm.dose`
- `unit` → `editForm.doseUnit` / `addForm.doseUnit`

#### 触发时机
1. **编辑医嘱时**：如果检测到是药品类医嘱且有 remark 字段，自动解析并填充
2. **备注字段失焦时**：用户编辑备注字段后失焦时触发解析

## 使用示例

### 1. 编辑医嘱时自动解析
```javascript
// 当编辑医嘱时，如果是药品类医嘱且有remark字段
if (isDrugOrder(item.orderClassCode) && item.remark) {
  const parsedData = parseRemarkField(item.remark)
  if (parsedData.success) {
    autoFillDrugInfo(parsedData, editForm)
  }
}
```

### 2. 手动输入备注信息
用户可以在备注字段中输入格式化的药品信息：
```
阿司匹林肠溶片100mg##每日一次##口服##1片##片
```

当用户失焦时，系统会自动解析并填充到对应的选择框中。

### 3. 医嘱类型变化时动态加载
```javascript
// 当医嘱类型改变时
const handleOrderCategoryChange = async (value) => {
  // 更新类型名称
  editForm.orderClassName = selectedOption.label
  
  // 动态加载对应的医嘱名称
  await loadMedicalOrderNames(value)
  
  // 清空医嘱名称选择
  editForm.orderItemName = ''
}
```

## 错误处理

### 1. 解析失败处理
- 如果 remark 字段格式不正确，会显示警告信息
- 部分解析成功时，会尽可能填充可用信息

### 2. 字典数据匹配失败
- 如果解析出的值在字典中找不到匹配项，不会填充该字段
- 不会影响其他字段的正常填充

### 3. 网络错误处理
- 字典数据加载失败时，会显示错误提示
- 不会影响现有功能的正常使用

## 配置说明

### 药品类医嘱类型配置
可以在 `isDrugOrder` 函数中修改药品类医嘱的识别规则：

```javascript
const drugOrderTypes = ['药品', '西药', '中药', '中成药', '药物', 'DRUG', 'MEDICINE']
```

### 字典数据过滤配置
可以在 `getMedicalOrderNamesByType` 方法中调整过滤逻辑，根据实际数据结构修改字段名：

```javascript
// 根据实际数据结构调整
if (item.orderClassCode) {
  return item.orderClassCode === orderClassCode
}
```

## 注意事项

1. **数据格式**：remark 字段必须严格按照 "规格##频次##用法##用量##单位" 的格式
2. **字典依赖**：自动填充功能依赖于字典数据的正确加载
3. **性能考虑**：解析和填充操作会有轻微的延迟，确保字典数据已加载完成
4. **兼容性**：新增功能不会影响现有的医嘱信息录入流程

## 扩展建议

1. **支持更多格式**：可以扩展解析器支持更多的分隔符格式
2. **智能匹配**：可以添加模糊匹配功能，提高字典匹配成功率
3. **批量处理**：可以添加批量解析和填充功能
4. **历史记录**：可以保存用户的解析历史，提供快速选择功能
